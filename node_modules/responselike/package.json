{"_from": "responselike@^2.0.0", "_id": "responselike@2.0.1", "_inBundle": false, "_integrity": "sha512-4gl03wn3hj1HP3yzgdI7d3lCkF95F21Pz4BPGvKHinyQzALR5CapwC8yIi0Rh58DEMQ/SguC03wFj2k0M/mHhw==", "_location": "/responselike", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "responselike@^2.0.0", "name": "responselike", "escapedName": "responselike", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/cacheable-request", "/got"], "_resolved": "https://registry.npmjs.org/responselike/-/responselike-2.0.1.tgz", "_shasum": "9a0bc8fdc252f3fb1cca68b016591059ba1422bc", "_spec": "responselike@^2.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/got", "author": {"name": "lukechilds"}, "bugs": {"url": "https://github.com/sindresorhus/responselike/issues"}, "bundleDependencies": false, "dependencies": {"lowercase-keys": "^2.0.0"}, "deprecated": false, "description": "A response-like object for mocking a Node.js HTTP response stream", "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.0", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^11.8.0", "xo": "^0.19.0"}, "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/responselike#readme", "keywords": ["http", "https", "response", "mock", "request", "responselike"], "license": "MIT", "main": "src/index.js", "name": "responselike", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/responselike.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "test": "xo && nyc ava"}, "version": "2.0.1", "xo": {"extends": "xo-lukechilds"}}