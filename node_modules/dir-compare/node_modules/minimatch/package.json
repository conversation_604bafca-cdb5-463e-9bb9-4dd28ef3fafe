{"_from": "minimatch@^3.0.4", "_id": "minimatch@3.1.2", "_inBundle": false, "_integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "_location": "/dir-compare/minimatch", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "minimatch@^3.0.4", "name": "minimatch", "escapedName": "minimatch", "rawSpec": "^3.0.4", "saveSpec": null, "fetchSpec": "^3.0.4"}, "_requiredBy": ["/dir-compare"], "_resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "_shasum": "19cd194bfd3e428f049a70817c038d89ab4be35b", "_spec": "minimatch@^3.0.4", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/dir-compare", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "bundleDependencies": false, "dependencies": {"brace-expansion": "^1.1.7"}, "deprecated": false, "description": "a glob matcher in javascript", "devDependencies": {"tap": "^15.1.6"}, "engines": {"node": "*"}, "files": ["minimatch.js"], "homepage": "https://github.com/isaacs/minimatch#readme", "license": "ISC", "main": "minimatch.js", "name": "minimatch", "publishConfig": {"tag": "v3-legacy"}, "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap"}, "version": "3.1.2"}