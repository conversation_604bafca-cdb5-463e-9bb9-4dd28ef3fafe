{"_from": "mime@^2.5.2", "_id": "mime@2.6.0", "_inBundle": false, "_integrity": "sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==", "_location": "/mime", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mime@^2.5.2", "name": "mime", "escapedName": "mime", "rawSpec": "^2.5.2", "saveSpec": null, "fetchSpec": "^2.5.2"}, "_requiredBy": ["/electron-publish"], "_resolved": "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz", "_shasum": "a2a682a95cd4d0cb1d6257e28f83da7e35800367", "_spec": "mime@^2.5.2", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/electron-publish", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}, "bin": {"mime": "cli.js"}, "bugs": {"url": "https://github.com/broofa/mime/issues"}, "bundleDependencies": false, "contributors": [], "dependencies": {}, "deprecated": false, "description": "A comprehensive library for mime-type mapping", "devDependencies": {"benchmark": "*", "chalk": "4.1.2", "eslint": "8.1.0", "mime-db": "1.50.0", "mime-score": "1.2.0", "mime-types": "2.1.33", "mocha": "9.1.3", "runmd": "*", "standard-version": "9.3.2"}, "engines": {"node": ">=4.0.0"}, "files": ["index.js", "lite.js", "Mime.js", "cli.js", "/types"], "homepage": "https://github.com/broofa/mime#readme", "keywords": ["util", "mime"], "license": "MIT", "name": "mime", "repository": {"url": "git+https://github.com/broofa/mime.git", "type": "git"}, "scripts": {"benchmark": "node src/benchmark.js", "md": "runmd --watch --output README.md src/README_js.md", "prepare": "node src/build.js && runmd --output README.md src/README_js.md", "release": "standard-version", "test": "mocha src/test.js"}, "version": "2.6.0"}