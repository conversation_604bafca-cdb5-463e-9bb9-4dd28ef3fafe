{"_from": "builder-util-runtime@9.2.4", "_id": "builder-util-runtime@9.2.4", "_inBundle": false, "_integrity": "sha512-upp+biKpN/XZMLim7aguUyW8s0FUpDvOtK6sbanMFDAMBzpHDqdhgVYm6zc9HJ6nWo7u2Lxk60i2M6Jd3aiNrA==", "_location": "/builder-util-runtime", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "builder-util-runtime@9.2.4", "name": "builder-util-runtime", "escapedName": "builder-util-runtime", "rawSpec": "9.2.4", "saveSpec": null, "fetchSpec": "9.2.4"}, "_requiredBy": ["/app-builder-lib", "/builder-util", "/dmg-builder", "/electron-builder", "/electron-publish"], "_resolved": "https://registry.npmjs.org/builder-util-runtime/-/builder-util-runtime-9.2.4.tgz", "_shasum": "13cd1763da621e53458739a1e63f7fcba673c42a", "_spec": "builder-util-runtime@9.2.4", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/electron-builder", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/electron-userland/electron-builder/issues"}, "bundleDependencies": false, "dependencies": {"debug": "^4.3.4", "sax": "^1.2.4"}, "deprecated": false, "description": "HTTP utilities. Used by [electron-builder](https://github.com/electron-userland/electron-builder).", "devDependencies": {"@types/debug": "4.1.7", "@types/sax": "1.2.3"}, "engines": {"node": ">=12.0.0"}, "files": ["out"], "homepage": "https://github.com/electron-userland/electron-builder", "license": "MIT", "main": "out/index.js", "name": "builder-util-runtime", "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git", "directory": "packages/builder-util-runtime"}, "types": "./out/index.d.ts", "version": "9.2.4"}