{"_from": "minipass@^7.1.2", "_id": "minipass@7.1.2", "_inBundle": false, "_integrity": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==", "_location": "/minipass", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "minipass@^7.1.2", "name": "minipass", "escapedName": "minipass", "rawSpec": "^7.1.2", "saveSpec": null, "fetchSpec": "^7.1.2"}, "_requiredBy": ["/config-file-ts/glob", "/path-scurry"], "_resolved": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "_shasum": "93a9626ce5e5e66bd4db86849e7515e92340a707", "_spec": "minipass@^7.1.2", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/config-file-ts/node_modules/glob", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "bundleDependencies": false, "deprecated": false, "description": "minimal implementation of a PassThrough stream", "devDependencies": {"@types/end-of-stream": "^1.4.2", "@types/node": "^20.1.2", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^19.0.0", "through2": "^2.0.3", "tshy": "^1.14.0", "typedoc": "^0.25.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist"], "homepage": "https://github.com/isaacs/minipass#readme", "keywords": ["passthrough", "stream"], "license": "ISC", "main": "./dist/commonjs/index.js", "name": "minipass", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "scripts": {"format": "prettier --write . --loglevel warn", "postversion": "npm publish", "prepare": "tshy", "prepublishOnly": "git push origin --follow-tags", "presnap": "npm run prepare", "pretest": "npm run prepare", "preversion": "npm test", "snap": "tap", "test": "tap", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "tap": {"typecheck": true, "include": ["test/*.ts"]}, "tshy": {"selfLink": false, "main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "version": "7.1.2"}