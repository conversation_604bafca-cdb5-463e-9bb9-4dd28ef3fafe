{"_from": "semver-compare@^1.0.0", "_id": "semver-compare@1.0.0", "_inBundle": false, "_integrity": "sha512-YM3/ITh2MJ5MtzaM429anh+x2jiLVjqILF4m4oyQB18W7Ggea7BfqdH/wGMK7dDiMghv/6WG7znWMwUDzJiXow==", "_location": "/semver-compare", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "semver-compare@^1.0.0", "name": "semver-compare", "escapedName": "semver-compare", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/roarr"], "_resolved": "https://registry.npmjs.org/semver-compare/-/semver-compare-1.0.0.tgz", "_shasum": "0dee216a1c941ab37e9efb1788f6afc5ff5537fc", "_spec": "semver-compare@^1.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/roarr", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/semver-compare/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "compare two semver version strings, returning -1, 0, or 1", "devDependencies": {"tape": "^3.0.0"}, "homepage": "https://github.com/substack/semver-compare", "keywords": ["semver", "compare", "cmp", "comparison", "sort"], "license": "MIT", "main": "index.js", "name": "semver-compare", "repository": {"type": "git", "url": "git://github.com/substack/semver-compare.git"}, "scripts": {"test": "tape test/*.js"}, "version": "1.0.0"}