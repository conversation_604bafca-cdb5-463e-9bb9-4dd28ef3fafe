{"_from": "fs-extra@^10.0.0", "_id": "fs-extra@10.1.0", "_inBundle": false, "_integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "_location": "/temp-file/fs-extra", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "fs-extra@^10.0.0", "name": "fs-extra", "escapedName": "fs-extra", "rawSpec": "^10.0.0", "saveSpec": null, "fetchSpec": "^10.0.0"}, "_requiredBy": ["/temp-file"], "_resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "_shasum": "02873cfbc4084dde127eaa5f9905eef2325d1abf", "_spec": "fs-extra@^10.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/temp-file", "author": {"name": "<PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, "bugs": {"url": "https://github.com/jprichardson/node-fs-extra/issues"}, "bundleDependencies": false, "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "deprecated": false, "description": "fs-extra contains methods that aren't included in the vanilla Node.js fs package. Such as recursive mkdir, copy, and remove.", "devDependencies": {"at-least-node": "^1.0.0", "klaw": "^2.1.1", "klaw-sync": "^3.0.2", "minimist": "^1.1.1", "mocha": "^5.0.5", "nyc": "^15.0.0", "proxyquire": "^2.0.1", "read-dir-files": "^0.1.1", "standard": "^16.0.3"}, "engines": {"node": ">=12"}, "files": ["lib/", "!lib/**/__tests__/"], "homepage": "https://github.com/jprichardson/node-fs-extra", "keywords": ["fs", "file", "file system", "copy", "directory", "extra", "mkdirp", "mkdir", "mkdirs", "recursive", "json", "read", "write", "extra", "delete", "remove", "touch", "create", "text", "output", "move", "promise"], "license": "MIT", "main": "./lib/index.js", "name": "fs-extra", "repository": {"type": "git", "url": "git+https://github.com/jprichardson/node-fs-extra.git"}, "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "test-find": "find ./lib/**/__tests__ -name *.test.js | xargs mocha", "unit": "nyc node test.js"}, "sideEffects": false, "version": "10.1.0"}