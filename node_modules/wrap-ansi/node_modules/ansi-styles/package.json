{"_from": "ansi-styles@^6.1.0", "_id": "ansi-styles@6.2.1", "_inBundle": false, "_integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==", "_location": "/wrap-ansi/ansi-styles", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ansi-styles@^6.1.0", "name": "ansi-styles", "escapedName": "ansi-styles", "rawSpec": "^6.1.0", "saveSpec": null, "fetchSpec": "^6.1.0"}, "_requiredBy": ["/wrap-ansi"], "_resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz", "_shasum": "0e62320cf99c21afff3b3012192546aacbfb05c5", "_spec": "ansi-styles@^6.1.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/wrap-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "bundleDependencies": false, "deprecated": false, "description": "ANSI escape codes for styling strings in the terminal", "devDependencies": {"ava": "^3.15.0", "svg-term-cli": "^2.1.1", "tsd": "^0.19.0", "xo": "^0.47.0"}, "engines": {"node": ">=12"}, "exports": "./index.js", "files": ["index.js", "index.d.ts"], "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "homepage": "https://github.com/chalk/ansi-styles#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "ansi-styles", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "scripts": {"screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor", "test": "xo && ava && tsd"}, "type": "module", "version": "6.2.1"}