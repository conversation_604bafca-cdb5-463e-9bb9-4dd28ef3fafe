{"_from": "wrap-ansi@^8.1.0", "_id": "wrap-ansi@8.1.0", "_inBundle": false, "_integrity": "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==", "_location": "/wrap-ansi", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "wrap-ansi@^8.1.0", "name": "wrap-ansi", "escapedName": "wrap-ansi", "rawSpec": "^8.1.0", "saveSpec": null, "fetchSpec": "^8.1.0"}, "_requiredBy": ["/@isaacs/cliui"], "_resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "_shasum": "56dc22368ee570face1b49819975d9b9a5ead214", "_spec": "wrap-ansi@^8.1.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/@isaacs/cliui", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "bundleDependencies": false, "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "deprecated": false, "description": "Wordwrap a string with ANSI escape codes", "devDependencies": {"ava": "^3.15.0", "chalk": "^4.1.2", "coveralls": "^3.1.1", "has-ansi": "^5.0.1", "nyc": "^15.1.0", "tsd": "^0.25.0", "xo": "^0.44.0"}, "engines": {"node": ">=12"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/chalk/wrap-ansi?sponsor=1", "homepage": "https://github.com/chalk/wrap-ansi#readme", "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "wrap-ansi", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "scripts": {"test": "xo && nyc ava && tsd"}, "type": "module", "version": "8.1.0"}