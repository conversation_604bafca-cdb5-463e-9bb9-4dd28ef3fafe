{"_from": "simple-update-notifier@2.0.0", "_id": "simple-update-notifier@2.0.0", "_inBundle": false, "_integrity": "sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==", "_location": "/simple-update-notifier", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "simple-update-notifier@2.0.0", "name": "simple-update-notifier", "escapedName": "simple-update-notifier", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/electron-builder"], "_resolved": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz", "_shasum": "d70b92bdab7d6d90dfd73931195a30b6e3d7cebb", "_spec": "simple-update-notifier@2.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/electron-builder", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "bundleDependencies": false, "dependencies": {"semver": "^7.5.3"}, "deprecated": false, "description": "Simple update notifier to check for npm updates for cli applications", "devDependencies": {"@babel/preset-env": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@release-it/conventional-changelog": "^5.1.1", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@typescript-eslint/eslint-plugin": "^5.60.0", "@typescript-eslint/parser": "^5.60.0", "eslint": "^8.43.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.5.0", "prettier": "^2.8.8", "release-it": "^15.11.0", "rollup": "^3.25.2", "rollup-plugin-ts": "^3.2.0", "typescript": "^5.1.3"}, "engines": {"node": ">=10"}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "files": ["build", "src"], "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "license": "MIT", "main": "build/index.js", "name": "simple-update-notifier", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "resolutions": {"semver": "^7.5.3"}, "scripts": {"build": "rollup -c rollup.config.js --bundleConfigAsCjs", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "prettier": "prettier --write src/**/*.ts", "prettier:check": "prettier --check src/**/*.ts", "release": "release-it", "test": "jest src --noStackTrace"}, "types": "build/index.d.ts", "version": "2.0.0"}