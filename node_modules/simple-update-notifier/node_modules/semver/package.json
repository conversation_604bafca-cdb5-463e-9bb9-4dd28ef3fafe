{"_from": "semver@^7.5.3", "_id": "semver@7.7.2", "_inBundle": false, "_integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "_location": "/simple-update-notifier/semver", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "semver@^7.5.3", "name": "semver", "escapedName": "semver", "rawSpec": "^7.5.3", "saveSpec": null, "fetchSpec": "^7.5.3"}, "_requiredBy": ["/simple-update-notifier"], "_resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "_shasum": "67d99fdcd35cec21e6f8b87a7fd515a33f982b58", "_spec": "semver@^7.5.3", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/simple-update-notifier", "author": {"name": "GitHub Inc."}, "bin": {"semver": "bin/semver.js"}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The semantic version parser used by npm.", "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.24.3", "benchmark": "^2.1.4", "tap": "^16.0.0"}, "engines": {"node": ">=10"}, "files": ["bin/", "lib/", "classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "homepage": "https://github.com/npm/node-semver#readme", "license": "ISC", "main": "index.js", "name": "semver", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "scripts": {"eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lint": "npm run eslint", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "snap": "tap", "template-oss-apply": "template-oss-apply --force", "test": "tap"}, "tap": {"timeout": 30, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.24.3", "engines": ">=10", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "publish": "true"}, "version": "7.7.2"}