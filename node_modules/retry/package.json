{"_from": "retry@^0.12.0", "_id": "retry@0.12.0", "_inBundle": false, "_integrity": "sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==", "_location": "/retry", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "retry@^0.12.0", "name": "retry", "escapedName": "retry", "rawSpec": "^0.12.0", "saveSpec": null, "fetchSpec": "^0.12.0"}, "_requiredBy": ["/promise-retry"], "_resolved": "https://registry.npmjs.org/retry/-/retry-0.12.0.tgz", "_shasum": "1b42a6266a21f07421d1b0b54b7dc167b01c013b", "_spec": "retry@^0.12.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/promise-retry", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "bugs": {"url": "https://github.com/tim-kos/node-retry/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Abstraction for exponential and custom retry strategies for failed operations.", "devDependencies": {"fake": "0.2.0", "istanbul": "^0.4.5", "tape": "^4.8.0"}, "directories": {"lib": "./lib"}, "engines": {"node": ">= 4"}, "homepage": "https://github.com/tim-kos/node-retry", "license": "MIT", "main": "index", "name": "retry", "repository": {"type": "git", "url": "git://github.com/tim-kos/node-retry.git"}, "scripts": {"release": "npm version ${SEMANTIC:-patch} -m \"Release %s\" && git push && git push --tags && npm publish", "release:major": "env SEMANTIC=major npm run release", "release:minor": "env SEMANTIC=minor npm run release", "release:patch": "env SEMANTIC=patch npm run release", "test": "istanbul cover ./node_modules/tape/bin/tape ./test/integration/*.js"}, "version": "0.12.0"}