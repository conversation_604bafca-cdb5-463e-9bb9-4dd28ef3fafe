{"_from": "package-json-from-dist@^1.0.0", "_id": "package-json-from-dist@1.0.1", "_inBundle": false, "_integrity": "sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==", "_location": "/package-json-from-dist", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "package-json-from-dist@^1.0.0", "name": "package-json-from-dist", "escapedName": "package-json-from-dist", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/config-file-ts/glob"], "_resolved": "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "_shasum": "4f1471a010827a86f94cfd9b0727e36d267de505", "_spec": "package-json-from-dist@^1.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/config-file-ts/node_modules/glob", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me"}, "bugs": {"url": "https://github.com/isaacs/package-json-from-dist/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Load the local package.json from either src or dist folder", "devDependencies": {"@types/node": "^20.12.12", "prettier": "^3.2.5", "tap": "^18.5.3", "tshy": "^1.14.0", "typedoc": "^0.24.8", "typescript": "^5.1.6"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist"], "homepage": "https://github.com/isaacs/package-json-from-dist#readme", "license": "BlueOak-1.0.0", "main": "./dist/commonjs/index.js", "name": "package-json-from-dist", "prettier": {"semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf", "experimentalTernaries": true}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/package-json-from-dist.git"}, "scripts": {"format": "prettier --write . --log-level warn", "postversion": "npm publish", "prepare": "tshy", "prepublishOnly": "git push origin --follow-tags", "presnap": "npm run prepare", "pretest": "npm run prepare", "preversion": "npm test", "snap": "tap", "test": "tap", "typedoc": "typedoc"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "version": "1.0.1"}