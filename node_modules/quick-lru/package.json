{"_from": "quick-lru@^5.1.1", "_id": "quick-lru@5.1.1", "_inBundle": false, "_integrity": "sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==", "_location": "/quick-lru", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "quick-lru@^5.1.1", "name": "quick-lru", "escapedName": "quick-lru", "rawSpec": "^5.1.1", "saveSpec": null, "fetchSpec": "^5.1.1"}, "_requiredBy": ["/http2-wrapper"], "_resolved": "https://registry.npmjs.org/quick-lru/-/quick-lru-5.1.1.tgz", "_shasum": "366493e6b3e42a3a6885e2e99d18f80fb7a8c932", "_spec": "quick-lru@^5.1.1", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/http2-wrapper", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Simple “Least Recently Used” (LRU) cache", "devDependencies": {"ava": "^2.0.0", "coveralls": "^3.0.3", "nyc": "^15.0.0", "tsd": "^0.11.0", "xo": "^0.26.0"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/quick-lru#readme", "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "license": "MIT", "name": "quick-lru", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "scripts": {"test": "xo && nyc ava && tsd"}, "version": "5.1.1"}