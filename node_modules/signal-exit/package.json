{"_from": "signal-exit@^4.0.1", "_id": "signal-exit@4.1.0", "_inBundle": false, "_integrity": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==", "_location": "/signal-exit", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "signal-exit@^4.0.1", "name": "signal-exit", "escapedName": "signal-exit", "rawSpec": "^4.0.1", "saveSpec": null, "fetchSpec": "^4.0.1"}, "_requiredBy": ["/foreground-child"], "_resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz", "_shasum": "952188c1cbd546070e2dd20d0f41c0ae0530cb04", "_spec": "signal-exit@^4.0.1", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/foreground-child", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "./dist/mjs/browser.js", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "bundleDependencies": false, "deprecated": false, "description": "when you want to fire an event no matter how a process exits.", "devDependencies": {"@types/cross-spawn": "^6.0.2", "@types/node": "^18.15.11", "@types/signal-exit": "^3.0.1", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.8.6", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.23.28", "typescript": "^5.0.2"}, "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./signals": {"import": {"types": "./dist/mjs/signals.d.ts", "default": "./dist/mjs/signals.js"}, "require": {"types": "./dist/cjs/signals.d.ts", "default": "./dist/cjs/signals.js"}}, "./browser": {"import": {"types": "./dist/mjs/browser.d.ts", "default": "./dist/mjs/browser.js"}, "require": {"types": "./dist/cjs/browser.d.ts", "default": "./dist/cjs/browser.js"}}}, "files": ["dist"], "funding": {"url": "https://github.com/sponsors/isaacs"}, "homepage": "https://github.com/tapjs/signal-exit#readme", "keywords": ["signal", "exit"], "license": "ISC", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "name": "signal-exit", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "scripts": {"format": "prettier --write . --loglevel warn", "postversion": "npm publish", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "preprepare": "rm -rf dist", "prepublishOnly": "git push origin --follow-tags", "presnap": "npm run prepare", "pretest": "npm run prepare", "preversion": "npm test", "snap": "c8 tap", "test": "c8 tap", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "tap": {"coverage": false, "jobs": 1, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "types": "./dist/mjs/index.d.ts", "version": "4.1.0"}