{"_from": "wrap-ansi-cjs@npm:wrap-ansi@^7.0.0", "_id": "wrap-ansi@7.0.0", "_inBundle": false, "_integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "_location": "/wrap-ansi-cjs", "_phantomChildren": {"is-fullwidth-code-point": "3.0.0"}, "_requested": {"type": "alias", "registry": true, "raw": "wrap-ansi-cjs@npm:wrap-ansi@^7.0.0", "name": "wrap-ansi-cjs", "escapedName": "wrap-ansi-cjs", "rawSpec": "npm:wrap-ansi@^7.0.0", "saveSpec": null, "fetchSpec": null, "subSpec": {"type": "range", "registry": true, "raw": "wrap-ansi@^7.0.0", "name": "wrap-ansi", "escapedName": "wrap-ansi", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}}, "_requiredBy": ["/@isaacs/cliui"], "_resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "_shasum": "67e145cff510a6a6984bdf1152911d69d2eb9e43", "_spec": "wrap-ansi-cjs@npm:wrap-ansi@^7.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/@isaacs/cliui", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "bundleDependencies": false, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "deprecated": false, "description": "Wordwrap a string with ANSI escape codes", "devDependencies": {"ava": "^2.1.0", "chalk": "^4.0.0", "coveralls": "^3.0.3", "has-ansi": "^4.0.0", "nyc": "^15.0.1", "xo": "^0.29.1"}, "engines": {"node": ">=10"}, "files": ["index.js"], "funding": "https://github.com/chalk/wrap-ansi?sponsor=1", "homepage": "https://github.com/chalk/wrap-ansi#readme", "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "wrap-ansi", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "scripts": {"test": "xo && nyc ava"}, "version": "7.0.0"}