{"_from": "shebang-command@^2.0.0", "_id": "shebang-command@2.0.0", "_inBundle": false, "_integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "_location": "/shebang-command", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "shebang-command@^2.0.0", "name": "shebang-command", "escapedName": "shebang-command", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/cross-spawn"], "_resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "_shasum": "ccd0af4f8835fbdc265b82461aaf0c36663f34ea", "_spec": "shebang-command@^2.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/cross-spawn", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "bugs": {"url": "https://github.com/kevva/shebang-command/issues"}, "bundleDependencies": false, "dependencies": {"shebang-regex": "^3.0.0"}, "deprecated": false, "description": "Get the command from a shebang", "devDependencies": {"ava": "^2.3.0", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js"], "homepage": "https://github.com/kevva/shebang-command#readme", "keywords": ["cmd", "command", "parse", "shebang"], "license": "MIT", "name": "shebang-command", "repository": {"type": "git", "url": "git+https://github.com/kevva/shebang-command.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.0"}