{"_from": "yallist@^4.0.0", "_id": "yallist@4.0.0", "_inBundle": false, "_integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "_location": "/yallist", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "yallist@^4.0.0", "name": "yallist", "escapedName": "yallist", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/fs-minipass/minipass", "/lru-cache", "/minizlib", "/minizlib/minipass", "/tar"], "_resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "_shasum": "9bb92790d9c0effec63be73519e11a35019a3a72", "_spec": "yallist@^4.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/lru-cache", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Yet Another Linked List", "devDependencies": {"tap": "^12.1.0"}, "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "homepage": "https://github.com/isaacs/yallist#readme", "license": "ISC", "main": "yallist.js", "name": "yallist", "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js --100"}, "version": "4.0.0"}