{"_from": "smart-buffer@^4.0.2", "_id": "smart-buffer@4.2.0", "_inBundle": false, "_integrity": "sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==", "_location": "/smart-buffer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "smart-buffer@^4.0.2", "name": "smart-buffer", "escapedName": "smart-buffer", "rawSpec": "^4.0.2", "saveSpec": null, "fetchSpec": "^4.0.2"}, "_requiredBy": ["/dmg-license"], "_resolved": "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz", "_shasum": "6e1d71fa4f18c05f7d0ff216dd16a481d0e8d9ae", "_spec": "smart-buffer@^4.0.2", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/dmg-license", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/JoshGlazebrook/smart-buffer/issues"}, "bundleDependencies": false, "contributors": [{"name": "syvita"}], "dependencies": {}, "deprecated": false, "description": "smart-buffer is a Buffer wrapper that adds automatic read & write offset tracking, string operations, data insertions, and more.", "devDependencies": {"@types/chai": "4.1.7", "@types/mocha": "5.2.7", "@types/node": "^12.0.0", "chai": "4.2.0", "coveralls": "3.0.5", "istanbul": "^0.4.5", "mocha": "6.2.0", "mocha-lcov-reporter": "^1.3.0", "nyc": "14.1.1", "source-map-support": "0.5.12", "ts-node": "8.3.0", "tslint": "5.18.0", "typescript": "^3.2.1"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "homepage": "https://github.com/JoshGlazebrook/smart-buffer/", "keywords": ["buffer", "smart", "packet", "serialize", "network", "cursor", "simple"], "license": "MIT", "main": "build/smartbuffer.js", "name": "smart-buffer", "nyc": {"extension": [".ts", ".tsx"], "include": ["src/*.ts", "src/**/*.ts"], "exclude": ["**.*.d.ts", "node_modules", "typings"], "require": ["ts-node/register"], "reporter": ["json", "html"], "all": true}, "repository": {"type": "git", "url": "git+https://github.com/JoshGlazebrook/smart-buffer.git"}, "scripts": {"build": "tsc -p ./", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "lint": "tslint --type-check --project tsconfig.json 'src/**/*.ts'", "prepublish": "npm install -g typescript && npm run build", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts"}, "typings": "typings/smartbuffer.d.ts", "version": "4.2.0"}