{"_from": "matcher@^3.0.0", "_id": "matcher@3.0.0", "_inBundle": false, "_integrity": "sha512-OkeDaAZ/bQCxeFAozM55PKcKU0yJMPGifLwV4Qgjitu+5MoAfSQN4lsLJeXZ1b8w0x+/Emda6MZgXS1jvsapng==", "_location": "/matcher", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "matcher@^3.0.0", "name": "matcher", "escapedName": "matcher", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/global-agent"], "_resolved": "https://registry.npmjs.org/matcher/-/matcher-3.0.0.tgz", "_shasum": "bd9060f4c5b70aa8041ccc6f80368760994f30ca", "_spec": "matcher@^3.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/global-agent", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/matcher/issues"}, "bundleDependencies": false, "dependencies": {"escape-string-regexp": "^4.0.0"}, "deprecated": false, "description": "Simple wildcard matching", "devDependencies": {"ava": "^2.4.0", "matcha": "^0.7.0", "tsd": "^0.11.0", "xo": "^0.30.0"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/matcher#readme", "keywords": ["matcher", "matching", "match", "regex", "regexp", "regular", "expression", "wildcard", "pattern", "string", "filter", "glob", "globber", "globbing", "minimatch"], "license": "MIT", "name": "matcher", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/matcher.git"}, "scripts": {"bench": "matcha bench.js", "test": "xo && ava && tsd"}, "version": "3.0.0", "xo": {"rules": {"@typescript-eslint/member-ordering": "off"}}}