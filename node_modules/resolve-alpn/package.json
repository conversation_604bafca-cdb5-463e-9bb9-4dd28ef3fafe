{"_from": "resolve-alpn@^1.0.0", "_id": "resolve-alpn@1.2.1", "_inBundle": false, "_integrity": "sha512-0a1F4l73/ZFZOakJnQ3FvkJ2+gSTQWz/r2KE5OdDY0TxPm5h4GkqkWWfM47T7HsbnOtcJVEF4epCVy6u7Q3K+g==", "_location": "/resolve-alpn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "resolve-alpn@^1.0.0", "name": "resolve-alpn", "escapedName": "resolve-alpn", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/http2-wrapper"], "_resolved": "https://registry.npmjs.org/resolve-alpn/-/resolve-alpn-1.2.1.tgz", "_shasum": "b7adbdac3546aaaec20b45e7d8265927072726f9", "_spec": "resolve-alpn@^1.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/http2-wrapper", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/szmarczak/resolve-alpn/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Detects the ALPN protocol", "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "pem": "1.14.3", "xo": "^0.38.2"}, "files": ["index.js"], "homepage": "https://github.com/szmarczak/resolve-alpn#readme", "keywords": ["alpn", "tls", "socket", "http2"], "license": "MIT", "main": "index.js", "name": "resolve-alpn", "repository": {"type": "git", "url": "git+https://github.com/szmarczak/resolve-alpn.git"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "version": "1.2.1"}