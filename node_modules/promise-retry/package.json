{"_from": "promise-retry@^2.0.1", "_id": "promise-retry@2.0.1", "_inBundle": false, "_integrity": "sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==", "_location": "/promise-retry", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "promise-retry@^2.0.1", "name": "promise-retry", "escapedName": "promise-retry", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/@electron/notarize"], "_resolved": "https://registry.npmjs.org/promise-retry/-/promise-retry-2.0.1.tgz", "_shasum": "ff747a13620ab57ba688f5fc67855410c370da22", "_spec": "promise-retry@^2.0.1", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/@electron/notarize", "author": {"name": "IndigoUnited", "email": "<EMAIL>", "url": "http://indigounited.com"}, "bugs": {"url": "https://github.com/IndigoUnited/node-promise-retry/issues/"}, "bundleDependencies": false, "dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "deprecated": false, "description": "Retries a function that returns a promise, leveraging the power of the retry module.", "devDependencies": {"expect.js": "^0.3.1", "mocha": "^8.0.1", "sleep-promise": "^8.0.1"}, "engines": {"node": ">=10"}, "homepage": "https://github.com/IndigoUnited/node-promise-retry#readme", "keywords": ["retry", "promise", "backoff", "repeat", "replay"], "license": "MIT", "main": "index.js", "name": "promise-retry", "repository": {"type": "git", "url": "git://github.com/IndigoUnited/node-promise-retry.git"}, "scripts": {"test": "mocha --bail -t 10000"}, "version": "2.0.1"}