{"_from": "@isaacs/cliui@^8.0.2", "_id": "@isaacs/cliui@8.0.2", "_inBundle": false, "_integrity": "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==", "_location": "/@isaacs/cliui", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@isaacs/cliui@^8.0.2", "name": "@isaacs/cliui", "escapedName": "@isaacs%2fcliui", "scope": "@isaacs", "rawSpec": "^8.0.2", "saveSpec": null, "fetchSpec": "^8.0.2"}, "_requiredBy": ["/jackspeak"], "_resolved": "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz", "_shasum": "b37667b7bc181c168782259bab42474fbf52b550", "_spec": "@isaacs/cliui@^8.0.2", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/jackspeak", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "bundleDependencies": false, "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "deprecated": false, "description": "easily create complex multi-column command-line-interfaces", "devDependencies": {"@types/node": "^14.0.27", "@typescript-eslint/eslint-plugin": "^4.0.0", "@typescript-eslint/parser": "^4.0.0", "c8": "^7.3.0", "chai": "^4.2.0", "chalk": "^4.1.0", "cross-env": "^7.0.2", "eslint": "^7.6.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "gts": "^3.0.0", "mocha": "^10.0.0", "rimraf": "^3.0.2", "rollup": "^2.23.1", "rollup-plugin-ts": "^3.0.2", "standardx": "^7.0.0", "typescript": "^4.0.0"}, "engines": {"node": ">=12"}, "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "files": ["build", "index.mjs", "!*.d.ts"], "homepage": "https://github.com/yargs/cliui#readme", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "license": "ISC", "main": "build/index.cjs", "module": "./index.mjs", "name": "@isaacs/cliui", "repository": {"type": "git", "url": "git+https://github.com/yargs/cliui.git"}, "scripts": {"build:cjs": "rollup -c", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "coverage": "c8 report --check-coverage", "fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "postcompile": "npm run build:cjs", "postest": "check", "precompile": "<PERSON><PERSON><PERSON> build", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 mocha ./test/*.cjs", "test:esm": "c8 mocha ./test/**/*.mjs"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "type": "module", "version": "8.0.2"}