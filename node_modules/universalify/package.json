{"_from": "universalify@^0.1.0", "_id": "universalify@0.1.2", "_inBundle": false, "_integrity": "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==", "_location": "/universalify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "universalify@^0.1.0", "name": "universalify", "escapedName": "universalify", "rawSpec": "^0.1.0", "saveSpec": null, "fetchSpec": "^0.1.0"}, "_requiredBy": ["/fs-extra"], "_resolved": "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz", "_shasum": "b646f69be3942dabcecc9d6639c80dc105efaa66", "_spec": "universalify@^0.1.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/fs-extra", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/RyanZim/universalify/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Make a callback- or promise-based function support both promises and callbacks.", "devDependencies": {"colortape": "^0.1.2", "coveralls": "^3.0.1", "nyc": "^10.2.0", "standard": "^10.0.1", "tape": "^4.6.3"}, "engines": {"node": ">= 4.0.0"}, "files": ["index.js"], "homepage": "https://github.com/RyanZim/universalify#readme", "keywords": ["callback", "native", "promise"], "license": "MIT", "name": "universalify", "repository": {"type": "git", "url": "git+https://github.com/RyanZim/universalify.git"}, "scripts": {"test": "standard && nyc tape test/*.js | colortape"}, "version": "0.1.2"}