{"_from": "yargs@^17.6.2", "_id": "yargs@17.7.2", "_inBundle": false, "_integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "_location": "/yargs", "_phantomChildren": {"is-fullwidth-code-point": "3.0.0"}, "_requested": {"type": "range", "registry": true, "raw": "yargs@^17.6.2", "name": "yargs", "escapedName": "yargs", "rawSpec": "^17.6.2", "saveSpec": null, "fetchSpec": "^17.6.2"}, "_requiredBy": ["/electron-builder"], "_resolved": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "_shasum": "991df39aca675a192b816e1e0363f9d75d2aa269", "_spec": "yargs@^17.6.2", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/electron-builder", "bugs": {"url": "https://github.com/yargs/yargs/issues"}, "bundleDependencies": false, "contributors": [{"name": "Yargs Contributors", "url": "https://github.com/yargs/yargs/graphs/contributors"}], "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "deprecated": false, "description": "yargs the modern, pirate-themed, successor to optimist.", "devDependencies": {"@types/chai": "^4.2.11", "@types/mocha": "^9.0.0", "@types/node": "^18.0.0", "c8": "^7.7.0", "chai": "^4.2.0", "chalk": "^4.0.0", "coveralls": "^3.0.9", "cpr": "^3.0.1", "cross-env": "^7.0.2", "cross-spawn": "^7.0.0", "eslint": "^7.23.0", "gts": "^3.0.0", "hashish": "0.0.4", "mocha": "^9.0.0", "rimraf": "^3.0.2", "rollup": "^2.23.0", "rollup-plugin-cleanup": "^3.1.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.4", "typescript": "^4.0.2", "which": "^2.0.0", "yargs-test-extends": "^1.0.1"}, "engines": {"node": ">=12"}, "exports": {"./package.json": "./package.json", ".": [{"import": "./index.mjs", "require": "./index.cjs"}, "./index.cjs"], "./helpers": {"import": "./helpers/helpers.mjs", "require": "./helpers/index.js"}, "./browser": {"import": "./browser.mjs", "types": "./browser.d.ts"}, "./yargs": [{"import": "./yargs.mjs", "require": "./yargs"}, "./yargs"]}, "files": ["browser.mjs", "browser.d.ts", "index.cjs", "helpers/*.js", "helpers/*", "index.mjs", "yargs", "yargs.mjs", "build", "locales", "LICENSE", "lib/platform-shims/*.mjs", "!*.d.ts", "!**/*.d.ts"], "homepage": "https://yargs.js.org/", "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "license": "MIT", "main": "./index.cjs", "module": "./index.mjs", "name": "yargs", "repository": {"type": "git", "url": "git+https://github.com/yargs/yargs.git"}, "scripts": {"build:cjs": "rollup -c rollup.config.cjs", "check": "gts lint && npm run check:js", "check:js": "eslint . --ext cjs --ext mjs --ext js", "clean": "gts clean", "compile": "rimraf build && tsc", "coverage": "c8 report --check-coverage", "fix": "gts fix && npm run fix:js", "fix:js": "eslint . --ext cjs --ext mjs --ext js --fix", "postbuild:cjs": "rimraf ./build/index.cjs.d.ts", "postcompile": "npm run build:cjs", "posttest": "npm run check", "prepare": "npm run compile", "pretest": "npm run compile -- -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 mocha --enable-source-maps ./test/*.cjs --require ./test/before.cjs --timeout=12000 --check-leaks", "test:esm": "c8 mocha --enable-source-maps ./test/esm/*.mjs --check-leaks"}, "type": "module", "version": "17.7.2"}