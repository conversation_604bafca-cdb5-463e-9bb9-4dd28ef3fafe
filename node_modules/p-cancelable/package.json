{"_from": "p-cancelable@^2.0.0", "_id": "p-cancelable@2.1.1", "_inBundle": false, "_integrity": "sha512-BZOr3nRQHOntUjTrH8+Lh54smKHoHyur8We1V8DSMVrl5A2malOOwuJRnKRDjSnkoeBh4at6BwEnb5I7Jl31wg==", "_location": "/p-cancelable", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "p-cancelable@^2.0.0", "name": "p-cancelable", "escapedName": "p-cancelable", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/got"], "_resolved": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-2.1.1.tgz", "_shasum": "aab7fbd416582fa32a3db49859c122487c5ed2cf", "_spec": "p-cancelable@^2.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/got", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Create a promise that can be canceled", "devDependencies": {"ava": "^1.4.1", "delay": "^4.1.0", "promise.prototype.finally": "^3.1.0", "tsd": "^0.7.1", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "license": "MIT", "name": "p-cancelable", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.1.1"}