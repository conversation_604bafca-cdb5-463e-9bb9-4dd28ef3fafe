{"_from": "lodash@^4.17.15", "_id": "lodash@4.17.21", "_inBundle": false, "_integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "_location": "/lodash", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lodash@^4.17.15", "name": "lodash", "escapedName": "lodash", "rawSpec": "^4.17.15", "saveSpec": null, "fetchSpec": "^4.17.15"}, "_requiredBy": ["/@malept/flatpak-bundler"], "_resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "_shasum": "679591c564c3bffaae8454cf0b3df370c3d6911c", "_spec": "lodash@^4.17.15", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/@malept/flatpak-bundler", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "keywords": ["modules", "stdlib", "util"], "license": "MIT", "main": "lodash.js", "name": "lodash", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "version": "4.17.21"}