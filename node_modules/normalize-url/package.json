{"_from": "normalize-url@^6.0.1", "_id": "normalize-url@6.1.0", "_inBundle": false, "_integrity": "sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==", "_location": "/normalize-url", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "normalize-url@^6.0.1", "name": "normalize-url", "escapedName": "normalize-url", "rawSpec": "^6.0.1", "saveSpec": null, "fetchSpec": "^6.0.1"}, "_requiredBy": ["/cacheable-request"], "_resolved": "https://registry.npmjs.org/normalize-url/-/normalize-url-6.1.0.tgz", "_shasum": "40d0885b535deffe3f3147bec877d05fe4c5668a", "_spec": "normalize-url@^6.0.1", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/cacheable-request", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/normalize-url/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Normalize a URL", "devDependencies": {"ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.11.0", "xo": "^0.25.3"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/normalize-url#readme", "keywords": ["normalize", "url", "uri", "address", "string", "normalization", "normalisation", "query", "querystring", "simplify", "strip", "trim", "canonical"], "license": "MIT", "name": "normalize-url", "nyc": {"reporter": ["text", "lcov"]}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/normalize-url.git"}, "scripts": {"test": "xo && nyc ava && tsd"}, "version": "6.1.0"}