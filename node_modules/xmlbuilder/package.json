{"_from": "xmlbuilder@^15.1.1", "_id": "xmlbuilder@15.1.1", "_inBundle": false, "_integrity": "sha512-yMqGBqtXyeN1e3TGYvgNgDVZ3j84W4cwkOXQswghol6APgZWaff9lnbvN7MHYJOiXsvGPXtjTYJEiC9J2wv9Eg==", "_location": "/xmlbuilder", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "xmlbuilder@^15.1.1", "name": "xmlbuilder", "escapedName": "xmlbuilder", "rawSpec": "^15.1.1", "saveSpec": null, "fetchSpec": "^15.1.1"}, "_requiredBy": ["/@types/plist", "/plist"], "_resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-15.1.1.tgz", "_shasum": "9dcdce49eea66d8d10b42cae94a79c3c8d0c2ec5", "_spec": "xmlbuilder@^15.1.1", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/plist", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "http://github.com/oozcitak/xmlbuilder-js/issues"}, "bundleDependencies": false, "contributors": [], "dependencies": {}, "deprecated": false, "description": "An XML builder for node.js", "devDependencies": {"coffee-coverage": "*", "coffeescript": "2.4.1", "coveralls": "*", "git-state": "*", "istanbul": "*", "mocha": "*", "nyc": "*", "xpath": "*"}, "engines": {"node": ">=8.0"}, "homepage": "http://github.com/oozcitak/xmlbuilder-js", "keywords": ["xml", "xmlbuilder"], "license": "MIT", "main": "./lib/index", "mocha": {"require": ["coffeescript/register", "coffee-coverage/register-istanbul", "test/common.coffee"], "recursive": true, "ui": "tdd", "reporter": "dot"}, "name": "xmlbuilder", "repository": {"type": "git", "url": "git://github.com/oozcitak/xmlbuilder-js.git"}, "scripts": {"perf": "coffee ./perf/index.coffee", "prepublishOnly": "coffee -co lib src", "test": "nyc mocha \"test/**/*.coffee\""}, "typings": "./typings/index.d.ts", "version": "15.1.1"}