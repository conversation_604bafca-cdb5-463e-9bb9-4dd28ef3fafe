{"_from": "utf8-byte-length@^1.0.1", "_id": "utf8-byte-length@1.0.5", "_inBundle": false, "_integrity": "sha512-Xn0w3MtiQ6zoz2vFyUVruaCL53O/DwUvkEeOvj+uulMm0BkUGYWmBYVyElqZaSLhY6ZD0ulfU3aBra2aVT4xfA==", "_location": "/utf8-byte-length", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "utf8-byte-length@^1.0.1", "name": "utf8-byte-length", "escapedName": "utf8-byte-length", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/truncate-utf8-bytes"], "_resolved": "https://registry.npmjs.org/utf8-byte-length/-/utf8-byte-length-1.0.5.tgz", "_shasum": "f9f63910d15536ee2b2d5dd4665389715eac5c1e", "_spec": "utf8-byte-length@^1.0.1", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/truncate-utf8-bytes", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "browser.js", "bugs": {"url": "https://github.com/parshap/utf8-byte-length/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Get utf8 byte length of string", "devDependencies": {"tape": "^4.2.2"}, "homepage": "https://github.com/parshap/utf8-byte-length#readme", "keywords": ["utf8"], "license": "(WTFPL OR MIT)", "main": "index.js", "name": "utf8-byte-length", "repository": {"type": "git", "url": "git+https://github.com/parshap/utf8-byte-length.git"}, "scripts": {"test": "tape test.js"}, "version": "1.0.5"}