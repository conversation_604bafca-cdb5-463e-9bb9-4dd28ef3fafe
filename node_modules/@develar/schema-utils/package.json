{"_from": "@develar/schema-utils@~2.6.5", "_id": "@develar/schema-utils@2.6.5", "_inBundle": false, "_integrity": "sha512-0cp4PsWQ/9avqTVMCtZ+GirikIA36ikvjtHweU4/j8yLtgObI0+JUPhYFScgwlteveGB1rt3Cm8UhN04XayDig==", "_location": "/@develar/schema-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@develar/schema-utils@~2.6.5", "name": "@develar/schema-utils", "escapedName": "@develar%2fschema-utils", "scope": "@develar", "rawSpec": "~2.6.5", "saveSpec": null, "fetchSpec": "~2.6.5"}, "_requiredBy": ["/app-builder-lib"], "_resolved": "https://registry.npmjs.org/@develar/schema-utils/-/schema-utils-2.6.5.tgz", "_shasum": "3ece22c5838402419a6e0425f85742b961d9b6c6", "_spec": "@develar/schema-utils@~2.6.5", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/app-builder-lib", "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "bundleDependencies": false, "dependencies": {"ajv": "^6.12.0", "ajv-keywords": "^3.4.1"}, "deprecated": false, "description": "webpack Validation Utils", "devDependencies": {"@babel/cli": "^7.8.3", "@babel/core": "^7.8.3", "@babel/preset-env": "^7.8.3", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/json-schema": "^7.0.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.3", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "eslint-plugin-import": "^2.20.0", "husky": "^4.0.10", "jest": "^24.9.0", "jest-junit": "^10.0.0", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.0.1", "typescript": "^3.7.5"}, "engines": {"node": ">= 8.9.0"}, "files": ["dist", "declarations"], "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "homepage": "https://github.com/webpack/schema-utils", "keywords": ["webpack"], "license": "MIT", "main": "dist/index.js", "name": "@develar/schema-utils", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "scripts": {"build": "npm-run-all -p \"build:**\"", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "clean": "del-cli dist declarations", "commitlint": "commitlint --from=master", "defaults": "webpack-defaults", "lint": "npm-run-all -l -p \"lint:**\"", "lint:js": "eslint --cache .", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:types": "tsc --pretty --noEmit", "prebuild": "npm run clean", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "security": "npm audit", "start": "npm run build -- -w", "test": "npm run test:coverage", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch"}, "types": "declarations/index.d.ts", "version": "2.6.5"}