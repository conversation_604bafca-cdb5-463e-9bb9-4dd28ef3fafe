{"_from": "lowercase-keys@^2.0.0", "_id": "lowercase-keys@2.0.0", "_inBundle": false, "_integrity": "sha512-tqNXrS78oMOE73NMxK4EMLQsQowWf8jKooH9g7xPavRT706R6bkQJ6DY2Te7QukaZsulxa30wQ7bk0pm4XiHmA==", "_location": "/lowercase-keys", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lowercase-keys@^2.0.0", "name": "lowercase-keys", "escapedName": "lowercase-keys", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/cacheable-request", "/got", "/responselike"], "_resolved": "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-2.0.0.tgz", "_shasum": "2603e78b7b4b0006cbca2fbcc8a3202558ac9479", "_spec": "lowercase-keys@^2.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/got", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/lowercase-keys/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Lowercase the keys of an object", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/lowercase-keys#readme", "keywords": ["object", "assign", "extend", "properties", "lowercase", "lower-case", "case", "keys", "key"], "license": "MIT", "name": "lowercase-keys", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/lowercase-keys.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.0.0"}