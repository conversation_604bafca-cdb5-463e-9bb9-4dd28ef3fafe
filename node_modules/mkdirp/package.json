{"_from": "mkdirp@^1.0.3", "_id": "mkdirp@1.0.4", "_inBundle": false, "_integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "_location": "/mkdirp", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mkdirp@^1.0.3", "name": "mkdirp", "escapedName": "mkdirp", "rawSpec": "^1.0.3", "saveSpec": null, "fetchSpec": "^1.0.3"}, "_requiredBy": ["/tar"], "_resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "_shasum": "3eb5ed62622756d79a5f0e2a221dfebad75c2f7e", "_spec": "mkdirp@^1.0.3", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/tar", "bin": {"mkdirp": "bin/cmd.js"}, "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Recursively mkdir, like `mkdir -p`", "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.7"}, "engines": {"node": ">=10"}, "files": ["bin", "lib", "index.js"], "homepage": "https://github.com/isaacs/node-mkdirp#readme", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "license": "MIT", "main": "index.js", "name": "mkdirp", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "scripts": {"postpublish": "git push origin --follow-tags", "postversion": "npm publish", "preversion": "npm test", "snap": "tap", "test": "tap"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "version": "1.0.4"}