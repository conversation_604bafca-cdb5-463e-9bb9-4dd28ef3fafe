{"_from": "y18n@^5.0.5", "_id": "y18n@5.0.8", "_inBundle": false, "_integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "_location": "/y18n", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "y18n@^5.0.5", "name": "y18n", "escapedName": "y18n", "rawSpec": "^5.0.5", "saveSpec": null, "fetchSpec": "^5.0.5"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "_shasum": "7f4934d0f7ca8c56f95314939ddcd2dd91ce1d55", "_spec": "y18n@^5.0.5", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "bundleDependencies": false, "deprecated": false, "description": "the bare-bones internationalization library used by yargs", "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^3.0.0", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^7.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "files": ["build", "index.mjs", "!*.d.ts"], "homepage": "https://github.com/yargs/y18n", "keywords": ["i18n", "internationalization", "yargs"], "license": "ISC", "main": "./build/index.cjs", "module": "./build/lib/index.js", "name": "y18n", "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "scripts": {"build:cjs": "rollup -c", "check": "standardx **/*.ts **/*.cjs **/*.mjs", "compile": "tsc", "coverage": "c8 report --check-coverage", "fix": "standardx --fix **/*.ts **/*.cjs **/*.mjs", "postcompile": "npm run build:cjs", "posttest": "npm run check", "precompile": "<PERSON><PERSON><PERSON> build", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs"}, "standardx": {"ignore": ["build"]}, "type": "module", "version": "5.0.8"}