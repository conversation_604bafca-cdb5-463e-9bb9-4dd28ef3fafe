{"_from": "progress@^2.0.3", "_id": "progress@2.0.3", "_inBundle": false, "_integrity": "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==", "_location": "/progress", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "progress@^2.0.3", "name": "progress", "escapedName": "progress", "rawSpec": "^2.0.3", "saveSpec": null, "fetchSpec": "^2.0.3"}, "_requiredBy": ["/@electron/get"], "_resolved": "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz", "_shasum": "7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8", "_spec": "progress@^2.0.3", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/@electron/get", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Jordan Scales", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "deprecated": false, "description": "Flexible ascii progress bar", "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/visionmedia/node-progress#readme", "keywords": ["cli", "progress"], "license": "MIT", "main": "./index.js", "name": "progress", "repository": {"type": "git", "url": "git://github.com/visionmedia/node-progress.git"}, "version": "2.0.3"}