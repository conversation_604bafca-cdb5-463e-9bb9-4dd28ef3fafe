{"_from": "mimic-response@^3.1.0", "_id": "mimic-response@3.1.0", "_inBundle": false, "_integrity": "sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==", "_location": "/decompress-response/mimic-response", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mimic-response@^3.1.0", "name": "mimic-response", "escapedName": "mimic-response", "rawSpec": "^3.1.0", "saveSpec": null, "fetchSpec": "^3.1.0"}, "_requiredBy": ["/decompress-response"], "_resolved": "https://registry.npmjs.org/mimic-response/-/mimic-response-3.1.0.tgz", "_shasum": "2d1d59af9c1b129815accc2c46a022a5ce1fa3c9", "_spec": "mimic-response@^3.1.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/decompress-response", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/mimic-response/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Mimic a Node.js HTTP response stream", "devDependencies": {"@types/node": "^14.0.1", "ava": "^2.4.0", "create-test-server": "^2.4.0", "p-event": "^4.1.0", "pify": "^5.0.0", "tsd": "^0.11.0", "xo": "^0.30.0"}, "engines": {"node": ">=10"}, "files": ["index.d.ts", "index.js"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/mimic-response#readme", "keywords": ["mimic", "response", "stream", "http", "https", "request", "get", "core"], "license": "MIT", "name": "mimic-response", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-response.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.1.0"}