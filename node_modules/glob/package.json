{"_from": "glob@^7.1.6", "_id": "glob@7.2.3", "_inBundle": false, "_integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "_location": "/glob", "_phantomChildren": {"brace-expansion": "1.1.12"}, "_requested": {"type": "range", "registry": true, "raw": "glob@^7.1.6", "name": "glob", "escapedName": "glob", "rawSpec": "^7.1.6", "saveSpec": null, "fetchSpec": "^7.1.6"}, "_requiredBy": ["/@electron/asar"], "_resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "_shasum": "b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b", "_spec": "glob@^7.1.6", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/@electron/asar", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/node-glob/issues"}, "bundleDependencies": false, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "deprecated": "Glob versions prior to v9 are no longer supported", "description": "a little globber", "devDependencies": {"memfs": "^3.2.0", "mkdirp": "0", "rimraf": "^2.2.8", "tap": "^15.0.6", "tick": "0.0.6"}, "engines": {"node": "*"}, "files": ["glob.js", "sync.js", "common.js"], "funding": {"url": "https://github.com/sponsors/isaacs"}, "homepage": "https://github.com/isaacs/node-glob#readme", "license": "ISC", "main": "glob.js", "name": "glob", "publishConfig": {"tag": "v7-legacy"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-glob.git"}, "scripts": {"bench": "bash benchmark.sh", "benchclean": "node benchclean.js", "prepublish": "npm run benchclean", "prof": "bash prof.sh && cat profile.txt", "profclean": "rm -f v8.log profile.txt", "test": "tap", "test-regen": "npm run profclean && TEST_REGEN=1 node test/00-setup.js"}, "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "jobs": 1}, "version": "7.2.3"}