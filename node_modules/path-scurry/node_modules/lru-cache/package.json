{"_from": "lru-cache@^10.2.0", "_id": "lru-cache@10.4.3", "_inBundle": false, "_integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==", "_location": "/path-scurry/lru-cache", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lru-cache@^10.2.0", "name": "lru-cache", "escapedName": "lru-cache", "rawSpec": "^10.2.0", "saveSpec": null, "fetchSpec": "^10.2.0"}, "_requiredBy": ["/path-scurry"], "_resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz", "_shasum": "410fc8a17b70e598013df257c2446b7f3383f119", "_spec": "lru-cache@^10.2.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/path-scurry", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A cache object that deletes the least-recently-used items.", "devDependencies": {"@types/node": "^20.2.5", "@types/tap": "^15.0.6", "benchmark": "^2.1.4", "esbuild": "^0.17.11", "eslint-config-prettier": "^8.5.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "prettier": "^2.6.2", "tap": "^20.0.3", "tshy": "^2.0.0", "tslib": "^2.4.0", "typedoc": "^0.25.3", "typescript": "^5.2.2"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "files": ["dist"], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "keywords": ["mru", "lru", "cache"], "license": "ISC", "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "name": "lru-cache", "prettier": {"semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "publishConfig": {"tag": "legacy-v10"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-lru-cache.git"}, "scripts": {"benchmark": "make -C benchmark", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh", "build": "npm run prepare", "format": "prettier --write .", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepare": "tshy && bash fixup.sh", "preprofile": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "presnap": "npm run prepare", "pretest": "npm run prepare", "preversion": "npm test", "profile": "make -C benchmark profile", "snap": "tap", "test": "tap", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts"}, "sideEffects": false, "tap": {"node-arg": ["--expose-gc"], "plugin": ["@tapjs/clock"]}, "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "version": "10.4.3"}