{"_from": "path-scurry@^1.11.1", "_id": "path-scurry@1.11.1", "_inBundle": false, "_integrity": "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==", "_location": "/path-scurry", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "path-scurry@^1.11.1", "name": "path-scurry", "escapedName": "path-scurry", "rawSpec": "^1.11.1", "saveSpec": null, "fetchSpec": "^1.11.1"}, "_requiredBy": ["/config-file-ts/glob"], "_resolved": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz", "_shasum": "7960a668888594a0720b12a911d1a742ab9f11d2", "_spec": "path-scurry@^1.11.1", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/config-file-ts/node_modules/glob", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://blog.izs.me"}, "bugs": {"url": "https://github.com/isaacs/path-scurry/issues"}, "bundleDependencies": false, "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "deprecated": false, "description": "walk paths fast and efficiently", "devDependencies": {"@nodelib/fs.walk": "^1.2.8", "@types/node": "^20.12.11", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "mkdirp": "^3.0.0", "prettier": "^3.2.5", "rimraf": "^5.0.1", "tap": "^18.7.2", "ts-node": "^10.9.2", "tshy": "^1.14.0", "typedoc": "^0.25.12", "typescript": "^5.4.3"}, "engines": {"node": ">=16 || 14 >=14.18"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist"], "funding": {"url": "https://github.com/sponsors/isaacs"}, "homepage": "https://github.com/isaacs/path-scurry#readme", "license": "BlueOak-1.0.0", "main": "./dist/commonjs/index.js", "name": "path-scurry", "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/path-scurry.git"}, "scripts": {"bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "postversion": "npm publish", "prepare": "tshy", "prepublishOnly": "git push origin --follow-tags", "presnap": "npm run prepare", "pretest": "npm run prepare", "preversion": "npm test", "snap": "tap", "test": "tap", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "tap": {"typecheck": true}, "tshy": {"selfLink": false, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "version": "1.11.1"}