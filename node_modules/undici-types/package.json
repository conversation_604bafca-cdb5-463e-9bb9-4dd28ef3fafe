{"_from": "undici-types@~5.26.4", "_id": "undici-types@5.26.5", "_inBundle": false, "_integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==", "_location": "/undici-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "undici-types@~5.26.4", "name": "undici-types", "escapedName": "undici-types", "rawSpec": "~5.26.4", "saveSpec": null, "fetchSpec": "~5.26.4"}, "_requiredBy": ["/@types/node"], "_resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "_shasum": "bcd539893d00b56e964fd2657a4866b221a65617", "_spec": "undici-types@~5.26.4", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/@types/node", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/dnlup"}, {"name": "<PERSON>", "url": "https://github.com/ethan-arrowood"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/KhafraDev"}, {"name": "<PERSON>", "url": "https://github.com/ronag"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/szmarczak"}, {"name": "<PERSON>", "url": "https://github.com/delvedor"}], "deprecated": false, "description": "A stand-alone types package for Undici", "files": ["*.d.ts"], "homepage": "https://undici.nodejs.org", "license": "MIT", "name": "undici-types", "repository": {"type": "git", "url": "git+https://github.com/nodejs/undici.git"}, "types": "index.d.ts", "version": "5.26.5"}