{"_from": "sax@^1.2.4", "_id": "sax@1.4.1", "_inBundle": false, "_integrity": "sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==", "_location": "/sax", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "sax@^1.2.4", "name": "sax", "escapedName": "sax", "rawSpec": "^1.2.4", "saveSpec": null, "fetchSpec": "^1.2.4"}, "_requiredBy": ["/builder-util-runtime"], "_resolved": "https://registry.npmjs.org/sax/-/sax-1.4.1.tgz", "_shasum": "44cc8988377f126304d3b3fc1010c733b929ef0f", "_spec": "sax@^1.2.4", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/builder-util-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "bundleDependencies": false, "deprecated": false, "description": "An evented streaming XML parser in JavaScript", "devDependencies": {"tap": "^15.1.6"}, "files": ["lib/sax.js", "LICENSE", "README.md"], "homepage": "https://github.com/isaacs/sax-js#readme", "license": "ISC", "main": "lib/sax.js", "name": "sax", "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js --cov -j4"}, "tap": {"statements": 79, "branches": 75, "functions": 80, "lines": 79}, "version": "1.4.1"}