{"_from": "minimist@^1.2.6", "_id": "minimist@1.2.8", "_inBundle": false, "_integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "_location": "/minimist", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "minimist@^1.2.6", "name": "minimist", "escapedName": "minimist", "rawSpec": "^1.2.6", "saveSpec": null, "fetchSpec": "^1.2.6"}, "_requiredBy": ["/@electron/osx-sign"], "_resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "_shasum": "c1a464e7693302e082a075cee0c057741ac4772c", "_spec": "minimist@^1.2.6", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/@electron/osx-sign", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/minimistjs/minimist/issues"}, "bundleDependencies": false, "deprecated": false, "description": "parse argument options", "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/minimistjs/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "license": "MIT", "main": "index.js", "name": "minimist", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/minimistjs/minimist.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=auto", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "version": "1.2.8"}