{"_from": "mimic-response@^1.0.0", "_id": "mimic-response@1.0.1", "_inBundle": false, "_integrity": "sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ==", "_location": "/mimic-response", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mimic-response@^1.0.0", "name": "mimic-response", "escapedName": "mimic-response", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/clone-response"], "_resolved": "https://registry.npmjs.org/mimic-response/-/mimic-response-1.0.1.tgz", "_shasum": "4923538878eef42063cb8a3e3b0798781487ab1b", "_spec": "mimic-response@^1.0.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/clone-response", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/mimic-response/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Mimic a Node.js HTTP response stream", "devDependencies": {"ava": "*", "create-test-server": "^0.1.0", "pify": "^3.0.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/mimic-response#readme", "keywords": ["mimic", "response", "stream", "http", "https", "request", "get", "core"], "license": "MIT", "name": "mimic-response", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-response.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.1"}