{"_from": "minimatch@^5.1.1", "_id": "minimatch@5.1.6", "_inBundle": false, "_integrity": "sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==", "_location": "/minimatch", "_phantomChildren": {"balanced-match": "1.0.2"}, "_requested": {"type": "range", "registry": true, "raw": "minimatch@^5.1.1", "name": "minimatch", "escapedName": "minimatch", "rawSpec": "^5.1.1", "saveSpec": null, "fetchSpec": "^5.1.1"}, "_requiredBy": ["/app-builder-lib", "/filelist"], "_resolved": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz", "_shasum": "1cfcb8cf5522ea69952cd2af95ae09477f122a96", "_spec": "minimatch@^5.1.1", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/app-builder-lib", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "bundleDependencies": false, "dependencies": {"brace-expansion": "^2.0.1"}, "deprecated": false, "description": "a glob matcher in javascript", "devDependencies": {"tap": "^16.3.2"}, "engines": {"node": ">=10"}, "files": ["minimatch.js", "lib"], "homepage": "https://github.com/isaacs/minimatch#readme", "license": "ISC", "main": "minimatch.js", "name": "minimatch", "publishConfig": {"tag": "legacy-v5"}, "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "scripts": {"postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "snap": "tap", "test": "tap"}, "version": "5.1.6"}