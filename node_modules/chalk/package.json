{"_from": "chalk@^4.1.2", "_id": "chalk@4.1.2", "_inBundle": false, "_integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "_location": "/chalk", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "chalk@^4.1.2", "name": "chalk", "escapedName": "chalk", "rawSpec": "^4.1.2", "saveSpec": null, "fetchSpec": "^4.1.2"}, "_requiredBy": ["/builder-util", "/electron-builder", "/electron-publish"], "_resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "_shasum": "aac4e2b7734a740867aeb16bf02aad556a1e7a01", "_spec": "chalk@^4.1.2", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/electron-builder", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "bundleDependencies": false, "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "deprecated": false, "description": "Terminal string styling done right", "devDependencies": {"ava": "^2.4.0", "coveralls": "^3.0.7", "execa": "^4.0.0", "import-fresh": "^3.1.0", "matcha": "^0.7.0", "nyc": "^15.0.0", "resolve-from": "^5.0.0", "tsd": "^0.7.4", "xo": "^0.28.2"}, "engines": {"node": ">=10"}, "files": ["source", "index.d.ts"], "funding": "https://github.com/chalk/chalk?sponsor=1", "homepage": "https://github.com/chalk/chalk#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "main": "source", "name": "chalk", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "scripts": {"bench": "matcha benchmark.js", "test": "xo && nyc ava && tsd"}, "version": "4.1.2", "xo": {"rules": {"unicorn/prefer-string-slice": "off", "unicorn/prefer-includes": "off", "@typescript-eslint/member-ordering": "off", "no-redeclare": "off", "unicorn/string-content": "off", "unicorn/better-regex": "off"}}}