{"_from": "yauzl@^2.10.0", "_id": "yauzl@2.10.0", "_inBundle": false, "_integrity": "sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==", "_location": "/yauzl", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "yauzl@^2.10.0", "name": "yauzl", "escapedName": "yauzl", "rawSpec": "^2.10.0", "saveSpec": null, "fetchSpec": "^2.10.0"}, "_requiredBy": ["/extract-zip"], "_resolved": "https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz", "_shasum": "c7eb17c93e112cb1086fa6d8e51fb0667b79a5f9", "_spec": "yauzl@^2.10.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/extract-zip", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "bundleDependencies": false, "dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}, "deprecated": false, "description": "yet another unzip library for node", "devDependencies": {"bl": "~1.0.0", "istanbul": "~0.3.4", "pend": "~1.2.0"}, "files": ["index.js"], "homepage": "https://github.com/thejoshwolfe/yauzl", "keywords": ["unzip", "zip", "stream", "archive", "file"], "license": "MIT", "main": "index.js", "name": "yauzl", "repository": {"type": "git", "url": "git+https://github.com/thejoshwolfe/yauzl.git"}, "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "version": "2.10.0"}