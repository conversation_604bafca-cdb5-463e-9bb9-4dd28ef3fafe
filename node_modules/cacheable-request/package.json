{"_from": "cacheable-request@^7.0.2", "_id": "cacheable-request@7.0.4", "_inBundle": false, "_integrity": "sha512-v+p6ongsrp0yTGbJXjgxPow2+DL93DASP4kXCDKb8/bwRtt9OEF3whggkkDkGNzgcWy2XaF4a8nZglC7uElscg==", "_location": "/cacheable-request", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cacheable-request@^7.0.2", "name": "cacheable-request", "escapedName": "cacheable-request", "rawSpec": "^7.0.2", "saveSpec": null, "fetchSpec": "^7.0.2"}, "_requiredBy": ["/got"], "_resolved": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-7.0.4.tgz", "_shasum": "7a33ebf08613178b403635be7b899d3e69bbe817", "_spec": "cacheable-request@^7.0.2", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/got", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "bundleDependencies": false, "dependencies": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^4.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^6.0.1", "responselike": "^2.0.0"}, "deprecated": false, "description": "Wrap native HTTP requests with RFC compliant cache support", "devDependencies": {"@keyv/sqlite": "^2.0.0", "ava": "^1.1.0", "coveralls": "^3.0.0", "create-test-server": "3.0.0", "delay": "^4.0.0", "eslint-config-xo-lukechilds": "^1.0.0", "nyc": "^14.1.1", "pify": "^4.0.0", "sqlite3": "^4.0.2", "this": "^1.0.2", "xo": "^0.23.0"}, "engines": {"node": ">=8"}, "files": ["src"], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "license": "MIT", "main": "src/index.js", "name": "cacheable-request", "repository": {"type": "git", "url": "git+https://github.com/lukechilds/cacheable-request.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "test": "xo && nyc ava"}, "version": "7.0.4", "xo": {"extends": "xo-lukechilds"}}