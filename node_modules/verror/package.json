{"_from": "verror@^1.10.0", "_id": "verror@1.10.1", "_inBundle": false, "_integrity": "sha512-veufcmxri4e3XSrT0xwfUR7kguIkaxBeosDg00yDWhk49wdwkSUrvvsm7nc75e1PUyvIeZj6nS8VQRYz2/S4Xg==", "_location": "/verror", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "verror@^1.10.0", "name": "verror", "escapedName": "verror", "rawSpec": "^1.10.0", "saveSpec": null, "fetchSpec": "^1.10.0"}, "_requiredBy": ["/dmg-license"], "_resolved": "https://registry.npmjs.org/verror/-/verror-1.10.1.tgz", "_shasum": "4bf09eeccf4563b109ed4b3d458380c972b0cdeb", "_spec": "verror@^1.10.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/dmg-license", "bugs": {"url": "https://github.com/joyent/node-verror/issues"}, "bundleDependencies": false, "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}, "deprecated": false, "description": "richer JavaScript errors", "engines": {"node": ">=0.6.0"}, "homepage": "https://github.com/joyent/node-verror#readme", "keywords": ["error", "errors", "err", "exception", "exceptions", "custom"], "license": "MIT", "main": "./lib/verror.js", "name": "verror", "repository": {"type": "git", "url": "git+https://github.com/joyent/node-verror.git"}, "scripts": {"test": "make test"}, "version": "1.10.1"}