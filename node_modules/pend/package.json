{"_from": "pend@~1.2.0", "_id": "pend@1.2.0", "_inBundle": false, "_integrity": "sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==", "_location": "/pend", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pend@~1.2.0", "name": "pend", "escapedName": "pend", "rawSpec": "~1.2.0", "saveSpec": null, "fetchSpec": "~1.2.0"}, "_requiredBy": ["/fd-slicer"], "_resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "_shasum": "7a57eb550a6783f9115331fcf4663d5c8e007a50", "_spec": "pend@~1.2.0", "_where": "/home/<USER>/文档/python/36yunframeElectron/node_modules/fd-slicer", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/andrewrk/node-pend/issues"}, "bundleDependencies": false, "deprecated": false, "description": "dead-simple optimistic async helper", "homepage": "https://github.com/andrewrk/node-pend#readme", "license": "MIT", "main": "index.js", "name": "pend", "repository": {"type": "git", "url": "git://github.com/andrewrk/node-pend.git"}, "scripts": {"test": "node test.js"}, "version": "1.2.0"}