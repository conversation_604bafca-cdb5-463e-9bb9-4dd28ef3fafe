const express = require('express');
const path = require('path');
const { spawn } = require('child_process');

const app = express();
const PORT = 3000;

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 主页路由 - 重定向到百度
app.get('/', (req, res) => {
    res.redirect('https://www.baidu.com');
});

// 本地kiosk页面（用于测试）
app.get('/kiosk', (req, res) => {
    res.sendFile(path.join(__dirname, 'kiosk.html'));
});

// 健康检查
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        message: '36yunframe Kiosk Server is running'
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 36yunframe Kiosk Server 启动成功!`);
    console.log(`📍 服务地址: http://localhost:${PORT}`);
    console.log(`🌐 将自动重定向到: https://www.baidu.com`);
    console.log(`🔧 测试页面: http://localhost:${PORT}/kiosk`);
    console.log(`💚 健康检查: http://localhost:${PORT}/health`);
    console.log('');
    console.log('按 Ctrl+C 停止服务器');
    
    // 如果不是开发模式，自动启动浏览器kiosk模式
    if (!process.argv.includes('--dev')) {
        console.log('🖥️  正在启动全屏kiosk模式...');
        setTimeout(() => {
            startKioskMode();
        }, 2000);
    }
});

// 启动kiosk模式
function startKioskMode() {
    // 尝试不同的浏览器命令
    const browsers = [
        // Chromium/Chrome 命令
        ['chromium-browser', '--kiosk', '--no-sandbox', '--disable-web-security', '--disable-features=VizDisplayCompositor', '--start-fullscreen', `http://localhost:${PORT}`],
        ['google-chrome', '--kiosk', '--no-sandbox', '--disable-web-security', '--start-fullscreen', `http://localhost:${PORT}`],
        ['chromium', '--kiosk', '--no-sandbox', '--disable-web-security', '--start-fullscreen', `http://localhost:${PORT}`],
        // Firefox 命令
        ['firefox', '--kiosk', `http://localhost:${PORT}`],
        // 通用浏览器命令
        ['x-www-browser', `http://localhost:${PORT}`],
        ['xdg-open', `http://localhost:${PORT}`]
    ];
    
    let browserStarted = false;
    
    for (const [command, ...args] of browsers) {
        try {
            console.log(`尝试启动浏览器: ${command} ${args.join(' ')}`);
            const browser = spawn(command, args, {
                detached: true,
                stdio: 'ignore'
            });
            
            browser.on('error', (err) => {
                console.log(`❌ ${command} 启动失败:`, err.message);
            });
            
            browser.on('spawn', () => {
                console.log(`✅ 成功启动浏览器: ${command}`);
                browserStarted = true;
                browser.unref();
            });
            
            // 等待一下看是否成功启动
            setTimeout(() => {
                if (browserStarted) return;
            }, 1000);
            
            if (browserStarted) break;
            
        } catch (error) {
            console.log(`❌ ${command} 不可用:`, error.message);
        }
    }
    
    if (!browserStarted) {
        console.log('⚠️  无法自动启动浏览器');
        console.log('请手动打开浏览器并访问:');
        console.log(`   http://localhost:${PORT}`);
        console.log('');
        console.log('或者使用以下命令手动启动kiosk模式:');
        console.log(`   chromium-browser --kiosk http://localhost:${PORT}`);
        console.log(`   firefox --kiosk http://localhost:${PORT}`);
    }
}

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 正在关闭服务器...');
    process.exit(0);
});
