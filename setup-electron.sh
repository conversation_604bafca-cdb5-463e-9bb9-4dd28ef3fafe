#!/bin/bash

# Electron 手动设置脚本

echo "=== Electron 手动设置脚本 ==="
echo ""

ELECTRON_VERSION="13.6.9"
ELECTRON_DIR="node_modules/electron"
ELECTRON_DIST_DIR="${ELECTRON_DIR}/dist"

# 检查是否存在下载的zip文件
if [ -f "electron-v${ELECTRON_VERSION}-linux-arm64.zip" ]; then
    echo "✅ 找到下载的Electron文件"
    
    # 创建目录
    echo "📁 创建目录结构..."
    mkdir -p "${ELECTRON_DIST_DIR}"
    mkdir -p "${ELECTRON_DIR}/node_modules/.bin"
    
    # 解压文件
    echo "📦 解压Electron..."
    unzip -q "electron-v${ELECTRON_VERSION}-linux-arm64.zip" -d "${ELECTRON_DIST_DIR}"
    
    if [ $? -ne 0 ]; then
        echo "❌ 解压失败"
        exit 1
    fi
    
    echo "✅ 解压完成"
    
elif [ -f "electron.zip" ]; then
    echo "✅ 找到electron.zip文件"
    
    # 创建目录
    echo "📁 创建目录结构..."
    mkdir -p "${ELECTRON_DIST_DIR}"
    mkdir -p "${ELECTRON_DIR}/node_modules/.bin"
    
    # 解压文件
    echo "📦 解压Electron..."
    unzip -q "electron.zip" -d "${ELECTRON_DIST_DIR}"
    
    if [ $? -ne 0 ]; then
        echo "❌ 解压失败"
        exit 1
    fi
    
    echo "✅ 解压完成"
    
else
    echo "❌ 未找到Electron zip文件"
    echo "请确保以下文件之一存在："
    echo "  - electron-v${ELECTRON_VERSION}-linux-arm64.zip"
    echo "  - electron.zip"
    echo ""
    echo "或者将下载的文件重命名为 electron.zip"
    exit 1
fi

# 设置可执行权限
echo "🔧 设置权限..."
chmod +x "${ELECTRON_DIST_DIR}/electron"

# 创建符号链接
echo "🔗 创建符号链接..."
ln -sf "../../dist/electron" "${ELECTRON_DIR}/node_modules/.bin/electron"

# 创建 package.json 文件
echo "📝 创建 Electron package.json..."
cat > "${ELECTRON_DIR}/package.json" << EOF
{
  "name": "electron",
  "version": "${ELECTRON_VERSION}",
  "description": "Build cross platform desktop apps with JavaScript, HTML, and CSS",
  "main": "index.js",
  "bin": {
    "electron": "cli.js"
  }
}
EOF

# 创建 index.js 文件
echo "📝 创建 Electron index.js..."
cat > "${ELECTRON_DIR}/index.js" << 'EOF'
const path = require('path');
module.exports = path.join(__dirname, 'dist', 'electron');
EOF

# 创建 cli.js 文件
echo "📝 创建 Electron cli.js..."
cat > "${ELECTRON_DIR}/cli.js" << 'EOF'
#!/usr/bin/env node
const spawn = require('child_process').spawn;
const path = require('path');

const electronPath = path.join(__dirname, 'dist', 'electron');
const args = process.argv.slice(2);

const child = spawn(electronPath, args, { stdio: 'inherit' });
child.on('close', (code) => {
  process.exit(code);
});
EOF

chmod +x "${ELECTRON_DIR}/cli.js"

# 验证安装
echo "🔍 验证安装..."
if [ -f "${ELECTRON_DIST_DIR}/electron" ]; then
    echo "✅ Electron 二进制文件存在"
    
    # 测试是否可执行
    if "${ELECTRON_DIST_DIR}/electron" --version &> /dev/null; then
        echo "✅ Electron 可以正常运行"
        echo "版本: $(${ELECTRON_DIST_DIR}/electron --version)"
    else
        echo "⚠️  Electron 二进制文件存在但可能无法运行"
        echo "请检查文件权限和系统兼容性"
    fi
else
    echo "❌ Electron 二进制文件不存在"
    exit 1
fi

echo ""
echo "✅ Electron 设置完成！"
echo ""
echo "现在可以运行："
echo "  ./start.sh        # 启动应用"
echo "  ./start-dev.sh    # 开发模式"
echo "  npm start         # 直接启动"
echo ""
echo "目录结构："
echo "  ${ELECTRON_DIST_DIR}/electron    # Electron 主程序"
echo "  ${ELECTRON_DIR}/package.json     # Electron 包信息"
echo "  ${ELECTRON_DIR}/index.js         # Electron 入口"
echo ""
