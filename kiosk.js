#!/usr/bin/env node

const { spawn } = require('child_process');
const http = require('http');

console.log('🚀 36yunframe Kiosk 启动器');
console.log('================================');

// 检查服务器是否运行
function checkServer(callback) {
    const req = http.get('http://localhost:3000/health', (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.status === 'ok') {
                    console.log('✅ 服务器已运行');
                    callback(true);
                } else {
                    callback(false);
                }
            } catch (error) {
                callback(false);
            }
        });
    });
    
    req.on('error', () => {
        callback(false);
    });
    
    req.setTimeout(2000, () => {
        req.destroy();
        callback(false);
    });
}

// 启动服务器
function startServer() {
    console.log('🔄 启动服务器...');
    const server = spawn('node', ['server.js'], {
        stdio: 'inherit',
        detached: false
    });
    
    server.on('error', (error) => {
        console.error('❌ 服务器启动失败:', error.message);
        process.exit(1);
    });
    
    // 等待服务器启动
    setTimeout(() => {
        checkServer((isRunning) => {
            if (isRunning) {
                console.log('✅ 服务器启动成功');
                startBrowser();
            } else {
                console.error('❌ 服务器启动失败');
                process.exit(1);
            }
        });
    }, 3000);
    
    return server;
}

// 启动浏览器
function startBrowser() {
    console.log('🌐 启动浏览器kiosk模式...');
    
    const browsers = [
        // Chromium/Chrome 全屏kiosk模式
        {
            command: 'chromium-browser',
            args: [
                '--kiosk',
                '--no-sandbox',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--start-fullscreen',
                '--disable-infobars',
                '--disable-session-crashed-bubble',
                '--disable-restore-session-state',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                'http://localhost:3000'
            ]
        },
        {
            command: 'google-chrome',
            args: [
                '--kiosk',
                '--no-sandbox',
                '--disable-web-security',
                '--start-fullscreen',
                '--disable-infobars',
                'http://localhost:3000'
            ]
        },
        {
            command: 'chromium',
            args: [
                '--kiosk',
                '--no-sandbox',
                '--disable-web-security',
                '--start-fullscreen',
                'http://localhost:3000'
            ]
        },
        // Firefox kiosk模式
        {
            command: 'firefox',
            args: ['--kiosk', 'http://localhost:3000']
        },
        // 备用选项
        {
            command: 'x-www-browser',
            args: ['http://localhost:3000']
        }
    ];
    
    let browserStarted = false;
    
    for (const browser of browsers) {
        if (browserStarted) break;
        
        try {
            console.log(`🔍 尝试启动: ${browser.command}`);
            
            const browserProcess = spawn(browser.command, browser.args, {
                detached: true,
                stdio: 'ignore'
            });
            
            browserProcess.on('error', (error) => {
                console.log(`❌ ${browser.command} 启动失败: ${error.message}`);
            });
            
            browserProcess.on('spawn', () => {
                console.log(`✅ 成功启动浏览器: ${browser.command}`);
                console.log('🖥️  应用程序已在全屏kiosk模式下运行');
                console.log('');
                console.log('📋 使用说明:');
                console.log('   • 应用将自动跳转到百度网站');
                console.log('   • 按 F11 可以切换全屏模式');
                console.log('   • 按 Ctrl+C 停止服务器');
                console.log('   • 关闭浏览器窗口退出kiosk模式');
                browserStarted = true;
                browserProcess.unref();
            });
            
            // 等待一下看是否成功启动
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.log(`❌ ${browser.command} 不可用: ${error.message}`);
        }
    }
    
    if (!browserStarted) {
        console.log('⚠️  无法自动启动浏览器');
        console.log('');
        console.log('请手动执行以下命令之一:');
        console.log('   chromium-browser --kiosk http://localhost:3000');
        console.log('   firefox --kiosk http://localhost:3000');
        console.log('   或直接在浏览器中访问: http://localhost:3000');
    }
}

// 主函数
function main() {
    // 检查服务器是否已经运行
    checkServer((isRunning) => {
        if (isRunning) {
            console.log('✅ 检测到服务器已运行');
            startBrowser();
        } else {
            console.log('🔄 服务器未运行，正在启动...');
            const serverProcess = startServer();
            
            // 处理退出信号
            process.on('SIGINT', () => {
                console.log('\n🛑 正在关闭应用...');
                serverProcess.kill();
                process.exit(0);
            });
            
            process.on('SIGTERM', () => {
                console.log('\n🛑 正在关闭应用...');
                serverProcess.kill();
                process.exit(0);
            });
        }
    });
}

// 启动应用
main();
