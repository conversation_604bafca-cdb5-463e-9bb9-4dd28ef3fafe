{"name": "36yunframe-electron", "version": "1.0.0", "description": "全屏Electron应用程序", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "fullscreen", "kiosk"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^13.6.9", "electron-builder": "^23.6.0"}, "build": {"appId": "com.example.36yunframe", "productName": "36yunframe", "directories": {"output": "dist"}, "files": ["main.js", "index.html", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}