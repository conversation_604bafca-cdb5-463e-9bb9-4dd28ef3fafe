<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>36yunframe Kiosk</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            overflow: hidden;
        }
        
        .container {
            text-align: center;
            padding: 2rem;
            max-width: 800px;
        }
        
        .logo {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.5rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .info-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .info-card h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        
        .info-card p {
            opacity: 0.8;
        }
        
        .redirect-info {
            background: rgba(255, 255, 255, 0.15);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }
        
        .countdown {
            font-size: 3rem;
            font-weight: bold;
            color: #FFD700;
            margin: 1rem 0;
        }
        
        .loading {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-top: 1rem;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .status {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        
        .time {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 1.1rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="time" id="currentTime"></div>
    
    <div class="container">
        <div class="logo">36yunframe</div>
        <div class="subtitle">全屏Kiosk应用程序</div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>🖥️ 分辨率</h3>
                <p id="resolution">1080 x 1920</p>
            </div>
            <div class="info-card">
                <h3>🌐 目标网站</h3>
                <p>百度 (baidu.com)</p>
            </div>
            <div class="info-card">
                <h3>⚡ 状态</h3>
                <p>运行中</p>
            </div>
            <div class="info-card">
                <h3>🔧 模式</h3>
                <p>全屏Kiosk</p>
            </div>
        </div>
        
        <div class="redirect-info">
            <h2>正在跳转到百度...</h2>
            <div class="countdown" id="countdown">5</div>
            <p>系统将在 <span id="countdownText">5</span> 秒后自动跳转</p>
            <div class="loading"></div>
        </div>
    </div>
    
    <div class="status">
        Kiosk Mode Active | Press F11 to toggle fullscreen
    </div>
    
    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        // 更新分辨率
        function updateResolution() {
            const resolution = `${window.screen.width} x ${window.screen.height}`;
            document.getElementById('resolution').textContent = resolution;
        }
        
        // 倒计时跳转
        let countdown = 5;
        function updateCountdown() {
            document.getElementById('countdown').textContent = countdown;
            document.getElementById('countdownText').textContent = countdown;
            
            if (countdown <= 0) {
                window.location.href = 'https://www.baidu.com';
                return;
            }
            
            countdown--;
            setTimeout(updateCountdown, 1000);
        }
        
        // 初始化
        updateTime();
        updateResolution();
        updateCountdown();
        
        // 每秒更新时间
        setInterval(updateTime, 1000);
        
        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            // F11 切换全屏
            if (event.key === 'F11') {
                event.preventDefault();
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    document.documentElement.requestFullscreen();
                }
            }
            
            // ESC 退出全屏
            if (event.key === 'Escape') {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                }
            }
            
            // 空格键立即跳转
            if (event.key === ' ') {
                event.preventDefault();
                window.location.href = 'https://www.baidu.com';
            }
        });
        
        // 自动进入全屏模式
        setTimeout(() => {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log('无法自动进入全屏模式:', err);
                });
            }
        }, 1000);
    </script>
</body>
</html>
