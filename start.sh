#!/bin/bash

# 36yunframe Kiosk App 启动脚本

echo "=== 36yunframe Kiosk App 启动脚本 ==="
echo ""

# 检查Node.js和npm是否已安装
if ! command -v node &> /dev/null; then
    echo "❌ 未找到Node.js，请先安装Node.js"
    echo "运行以下命令安装："
    echo "  ./install-nodejs.sh"
    echo "或访问 https://nodejs.org/ 手动下载安装"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ 未找到npm，请先安装npm"
    echo "运行以下命令安装："
    echo "  ./install-nodejs.sh"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"
echo "✅ npm 版本: $(npm --version)"
echo ""

# 检查是否安装了项目依赖
if [ ! -d "node_modules" ]; then
    echo "📦 检测到未安装项目依赖，正在安装..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
    echo ""
fi

# 启动应用
echo "🚀 启动Kiosk应用程序..."
echo "应用特性："
echo "  ✓ 轻量级基于浏览器的解决方案"
echo "  ✓ 自动全屏kiosk模式"
echo "  ✓ 自动跳转到百度网站"
echo "  ✓ 支持1080x1920分辨率"
echo ""
echo "控制说明："
echo "  F11: 切换全屏模式"
echo "  ESC: 退出全屏"
echo "  Ctrl+C: 停止服务器"
echo ""

node kiosk.js
