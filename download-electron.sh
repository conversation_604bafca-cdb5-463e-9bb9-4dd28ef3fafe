#!/bin/bash

# Electron 手动下载和安装脚本

echo "=== Electron 手动下载和安装脚本 ==="
echo ""

ELECTRON_VERSION="13.6.9"
ELECTRON_URL="https://github.com/electron/electron/releases/download/v${ELECTRON_VERSION}/electron-v${ELECTRON_VERSION}-linux-arm64.zip"
ELECTRON_DIR="node_modules/electron"
ELECTRON_DIST_DIR="${ELECTRON_DIR}/dist"

echo "📦 准备下载 Electron v${ELECTRON_VERSION} for ARM64 Linux"
echo "🔗 下载地址: ${ELECTRON_URL}"
echo ""

# 检查是否已安装依赖
if [ ! -f "package.json" ]; then
    echo "❌ 未找到 package.json 文件"
    echo "请确保在项目根目录运行此脚本"
    exit 1
fi

# 创建必要的目录
echo "📁 创建目录结构..."
mkdir -p "${ELECTRON_DIST_DIR}"
mkdir -p "${ELECTRON_DIR}/node_modules/.bin"

# 下载 Electron
echo "⬇️  开始下载 Electron..."
if command -v wget &> /dev/null; then
    wget -O electron.zip "${ELECTRON_URL}"
elif command -v curl &> /dev/null; then
    curl -L -o electron.zip "${ELECTRON_URL}"
else
    echo "❌ 未找到 wget 或 curl 命令"
    echo "请手动下载文件："
    echo "   ${ELECTRON_URL}"
    echo "然后将文件保存为 electron.zip 并重新运行此脚本"
    exit 1
fi

if [ $? -ne 0 ]; then
    echo "❌ 下载失败"
    echo "请检查网络连接或手动下载："
    echo "   ${ELECTRON_URL}"
    exit 1
fi

echo "✅ 下载完成"

# 解压文件
echo "📦 解压 Electron..."
if command -v unzip &> /dev/null; then
    unzip -q electron.zip -d "${ELECTRON_DIST_DIR}"
else
    echo "❌ 未找到 unzip 命令"
    echo "请安装 unzip: sudo apt install unzip"
    exit 1
fi

if [ $? -ne 0 ]; then
    echo "❌ 解压失败"
    exit 1
fi

echo "✅ 解压完成"

# 设置可执行权限
echo "🔧 设置权限..."
chmod +x "${ELECTRON_DIST_DIR}/electron"

# 创建符号链接
echo "🔗 创建符号链接..."
ln -sf "../../dist/electron" "${ELECTRON_DIR}/node_modules/.bin/electron"

# 创建 package.json 文件
echo "📝 创建 Electron package.json..."
cat > "${ELECTRON_DIR}/package.json" << EOF
{
  "name": "electron",
  "version": "${ELECTRON_VERSION}",
  "description": "Build cross platform desktop apps with JavaScript, HTML, and CSS",
  "main": "index.js",
  "bin": {
    "electron": "cli.js"
  }
}
EOF

# 创建 index.js 文件
echo "📝 创建 Electron index.js..."
cat > "${ELECTRON_DIR}/index.js" << 'EOF'
const path = require('path');
module.exports = path.join(__dirname, 'dist', 'electron');
EOF

# 创建 cli.js 文件
echo "📝 创建 Electron cli.js..."
cat > "${ELECTRON_DIR}/cli.js" << 'EOF'
#!/usr/bin/env node
const spawn = require('child_process').spawn;
const path = require('path');

const electronPath = path.join(__dirname, 'dist', 'electron');
const args = process.argv.slice(2);

const child = spawn(electronPath, args, { stdio: 'inherit' });
child.on('close', (code) => {
  process.exit(code);
});
EOF

chmod +x "${ELECTRON_DIR}/cli.js"

# 清理下载文件
echo "🧹 清理临时文件..."
rm -f electron.zip

echo ""
echo "✅ Electron 安装完成！"
echo ""
echo "现在可以运行："
echo "  ./start.sh        # 启动应用"
echo "  ./start-dev.sh    # 开发模式"
echo "  npm start         # 直接启动"
echo ""
echo "如果遇到问题，请检查："
echo "  ls -la ${ELECTRON_DIST_DIR}/electron"
echo ""
