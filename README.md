# 36yunframe Electron App

一个使用Electron构建的全屏应用程序，启动时自动打开百度网站。

## 功能特性

- 🖥️ 全屏显示 (1080x1920)
- 🔒 Kiosk模式，防止用户退出全屏
- 🌐 启动时自动加载百度网站
- 🚫 禁用窗口控制（最小化、最大化、关闭等）
- 🔝 始终置顶显示
- 🛡️ 安全配置，禁用不必要的功能

## 环境要求

在开始之前，请确保系统中已安装Node.js和npm：

### 安装Node.js (Ubuntu/Debian)
```bash
# 更新包列表
sudo apt update

# 安装Node.js和npm
sudo apt install nodejs npm

# 验证安装
node --version
npm --version
```

### 安装Node.js (其他系统)
- Windows/macOS: 访问 [Node.js官网](https://nodejs.org/) 下载安装包
- 或使用Node版本管理器如nvm

## 安装依赖

### 方法一：自动安装（可能很慢）
```bash
npm install
```

### 方法二：手动下载Electron（推荐）
如果npm安装Electron很慢，可以手动下载：

```bash
# 运行手动下载脚本
./download-electron.sh
```

或者手动操作：
1. 下载Electron: https://github.com/electron/electron/releases/download/v13.6.9/electron-v13.6.9-linux-arm64.zip
2. 解压到 `node_modules/electron/dist/` 目录
3. 设置可执行权限: `chmod +x node_modules/electron/dist/electron`

### 方法三：仅安装其他依赖
```bash
# 先安装除Electron外的其他依赖
npm install --ignore-scripts
# 然后手动下载Electron
./download-electron.sh
```

## 运行应用

### 快速启动（推荐）
```bash
./start.sh
```
自动启动HTTP服务器并打开浏览器kiosk模式

### 开发模式
```bash
./start-dev.sh
```
仅启动HTTP服务器，不自动打开浏览器，便于调试

### 手动启动
```bash
# 启动服务器
npm start

# 在另一个终端启动kiosk模式
node kiosk.js
```

## 使用说明

- **F11**: 切换全屏模式
- **ESC**: 退出全屏
- **空格键**: 立即跳转到百度（在启动页面）
- **Ctrl+C**: 停止服务器

## 项目结构

```
36yunframeElectron/
├── server.js            # HTTP服务器
├── kiosk.js            # Kiosk启动器
├── kiosk.html          # 启动页面
├── package.json        # 项目配置
├── start.sh           # 生产模式启动脚本
├── start-dev.sh       # 开发模式启动脚本
├── install-nodejs.sh  # Node.js安装脚本
└── README.md          # 说明文档
```

## 技术特点

### 轻量级架构
- **HTTP服务器**: 基于Express.js的轻量级服务器
- **浏览器Kiosk**: 利用系统浏览器的kiosk模式
- **无依赖**: 不需要下载大型Electron二进制文件

### 浏览器支持
- **Chromium/Chrome**: 完整kiosk模式支持
- **Firefox**: 基本kiosk模式支持
- **其他浏览器**: 降级到普通全屏模式

## 自定义配置

### 修改目标网站
编辑 `server.js` 文件中的重定向URL：
```javascript
app.get('/', (req, res) => {
    res.redirect('https://www.baidu.com');  // 修改这里
});
```

### 修改启动页面
编辑 `kiosk.html` 文件自定义启动页面的外观和倒计时时间。

### 修改服务器端口
编辑 `server.js` 文件中的端口配置：
```javascript
const PORT = 3000;  // 修改端口号
```

## 故障排除

### 浏览器无法启动
1. 确保系统已安装Chromium或Firefox
2. 尝试手动运行浏览器命令
3. 检查浏览器是否支持kiosk模式

### 服务器启动失败
1. 检查端口3000是否被占用
2. 确保Node.js和npm已正确安装
3. 运行 `npm install` 重新安装依赖

### 无法进入全屏模式
1. 按F11手动切换全屏
2. 检查浏览器是否允许全屏API
3. 尝试不同的浏览器
