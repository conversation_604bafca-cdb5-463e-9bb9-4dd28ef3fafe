#!/bin/bash

# 36yunframe Kiosk App 开发模式启动脚本

echo "=== 36yunframe Kiosk App 开发模式 ==="
echo ""

# 检查Node.js和npm是否已安装
if ! command -v node &> /dev/null; then
    echo "❌ 未找到Node.js，请先安装Node.js"
    echo "运行以下命令安装："
    echo "  ./install-nodejs.sh"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ 未找到npm，请先安装npm"
    echo "运行以下命令安装："
    echo "  ./install-nodejs.sh"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"
echo "✅ npm 版本: $(npm --version)"
echo ""

# 检查是否安装了项目依赖
if [ ! -d "node_modules" ]; then
    echo "📦 检测到未安装项目依赖，正在安装..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
    echo ""
fi

# 启动开发模式
echo "🛠️  启动开发模式..."
echo "开发模式功能："
echo "  ✓ 仅启动HTTP服务器，不自动打开浏览器"
echo "  ✓ 可以手动访问 http://localhost:3000"
echo "  ✓ 测试页面: http://localhost:3000/kiosk"
echo "  ✓ 健康检查: http://localhost:3000/health"
echo ""
echo "手动启动kiosk模式:"
echo "  chromium-browser --kiosk http://localhost:3000"
echo "  firefox --kiosk http://localhost:3000"
echo ""

npm run dev
